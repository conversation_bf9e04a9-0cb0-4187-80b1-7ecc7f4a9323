#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
卡妙视频编辑软件打包脚本
使用PyInstaller将项目打包成独立的exe文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def install_build_dependencies():
    """安装打包所需的依赖"""
    print("🔧 检查并安装打包依赖...")

    # 安装打包专用依赖
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements_build.txt"
        ])
        print("✅ 打包依赖安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包依赖安装失败: {e}")
        # 尝试单独安装PyInstaller
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller>=5.13.0"])
            print("✅ PyInstaller 安装成功")
            return True
        except subprocess.CalledProcessError:
            print("❌ PyInstaller 安装失败")
            return False

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print("✅ PyInstaller 已安装")
        return True
    except ImportError:
        print("❌ PyInstaller 未安装，正在安装...")
        return install_build_dependencies()

def create_spec_file():
    """创建PyInstaller规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import os
from pathlib import Path

# 获取项目根目录
project_root = Path(SPECPATH)

a = Analysis(
    ['main.py'],
    pathex=[str(project_root)],
    binaries=[
        # 包含FFmpeg可执行文件
        (str(project_root / 'ffmpeg_bundle' / 'bin' / 'ffmpeg.exe'), 'ffmpeg_bundle/bin'),
        (str(project_root / 'ffmpeg_bundle' / 'bin' / 'ffprobe.exe'), 'ffmpeg_bundle/bin'),
        (str(project_root / 'ffmpeg_bundle' / 'bin' / 'ffplay.exe'), 'ffmpeg_bundle/bin'),
    ],
    datas=[
        # 包含FFmpeg预设和文档
        (str(project_root / 'ffmpeg_bundle' / 'presets'), 'ffmpeg_bundle/presets'),
        (str(project_root / 'ffmpeg_bundle' / 'doc'), 'ffmpeg_bundle/doc'),
        (str(project_root / 'ffmpeg_bundle' / 'LICENSE'), 'ffmpeg_bundle'),
        (str(project_root / 'ffmpeg_bundle' / 'README.txt'), 'ffmpeg_bundle'),
        # 包含源代码模块
        (str(project_root / 'src'), 'src'),
    ],
    hiddenimports=[
        # 核心模块
        'src.core.video_deduplication',
        'src.core.timestamp_control', 
        'src.core.image_to_video',
        'src.core.ab_video',
        'src.gui.enhanced_window',
        'src.gui.drag_drop_frame',
        'src.ffmpeg_setup',
        'src.timestamp_processor',
        'src.video_processor',
        # 第三方库
        'cv2',
        'numpy',
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        'PIL.ImageFilter',
        'PIL.ImageEnhance',
        'matplotlib',
        'matplotlib.pyplot',
        'tqdm',
        'imageio',
        'imageio_ffmpeg',
        'tkinterdnd2',
        'ttkbootstrap',
        # 可能的隐藏依赖
        'scipy',
        'scipy.ndimage',
        'tempfile',
        'threading',
        'subprocess',
        'json',
        'hashlib',
        'pathlib',
        'glob',
        'random',
        'math',
        'typing',
    ],
    hookspath=['.'],  # 包含当前目录的hook文件
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除不需要的模块以减小体积（保留unittest因为scipy需要）
        'test',
        'tests',
        'doctest',
        'pdb',
        'pydoc',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='卡妙视频编辑软件',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,  # 保留控制台以显示调试信息
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='卡妙视频编辑软件',
)
'''
    
    with open('video_editor.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 规格文件 video_editor.spec 创建成功")

def build_executable():
    """构建可执行文件"""
    print("🔨 开始构建可执行文件...")
    print("这可能需要几分钟时间，请耐心等待...")
    
    try:
        # 使用规格文件构建
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "video_editor.spec"]
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 构建成功!")
            print("\n构建输出:")
            print(result.stdout)
            return True
        else:
            print("❌ 构建失败!")
            print("\n错误信息:")
            print(result.stderr)
            print("\n标准输出:")
            print(result.stdout)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中发生错误: {str(e)}")
        return False

def create_readme():
    """创建使用说明文件"""
    readme_content = """# 卡妙视频编辑软件 - 独立版本

## 📦 软件说明
这是卡妙视频编辑软件的独立可执行版本，无需安装Python环境即可运行。

## 🚀 使用方法
1. 解压所有文件到任意目录
2. 双击运行 "卡妙视频编辑软件.exe"
3. 首次运行可能需要几秒钟加载时间

## 💡 功能介绍
- **视频去重**: 通过特效绕过平台原创检测
- **时间戳控制**: 分段播放速度控制
- **图转视频**: 图片序列转视频，支持多种转场效果
- **AB视频**: 平台检测A视频，用户观看B视频
- **批量处理**: 支持所有功能的批量操作

## 📋 系统要求
- Windows 7/8/10/11 (64位)
- 至少 2GB 可用内存
- 至少 1GB 可用磁盘空间

## 🔧 故障排除
1. 如果程序无法启动，请检查是否有杀毒软件拦截
2. 如果处理大文件时内存不足，请关闭其他程序
3. 建议在SSD硬盘上运行以获得更好性能

## 📞 技术支持
如有问题请联系技术支持。

---
构建时间: {build_time}
版本信息: 独立可执行版本
"""
    
    import datetime
    build_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    with open('dist/卡妙视频编辑软件/README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content.format(build_time=build_time))
    
    print("✅ 使用说明文件创建成功")

def main():
    """主函数"""
    print("🎬 卡妙视频编辑软件打包工具")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists('main.py'):
        print("❌ 错误: 请在项目根目录运行此脚本")
        return
    
    # 检查PyInstaller
    if not check_pyinstaller():
        return
    
    # 创建规格文件
    create_spec_file()
    
    # 构建可执行文件
    if build_executable():
        # 创建说明文件
        if os.path.exists('dist/卡妙视频编辑软件'):
            create_readme()
            
            print("\n" + "=" * 50)
            print("🎉 打包完成!")
            print(f"📁 输出目录: {os.path.abspath('dist/卡妙视频编辑软件')}")
            print("📋 包含文件:")
            print("   - 卡妙视频编辑软件.exe (主程序)")
            print("   - ffmpeg_bundle/ (视频处理工具)")
            print("   - src/ (源代码模块)")
            print("   - 其他依赖文件")
            print("   - README.txt (使用说明)")
            print("\n💡 提示: 可以将整个文件夹复制到其他电脑上直接运行")
        else:
            print("⚠️ 构建完成但输出目录不存在，请检查构建日志")
    else:
        print("❌ 打包失败，请检查错误信息")

if __name__ == "__main__":
    main()
