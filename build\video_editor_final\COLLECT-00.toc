([('VideoEditor_Final.exe',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\build\\video_editor_final\\VideoEditor_Final.exe',
   'EXECUTABLE'),
  ('imageio_ffmpeg\\binaries\\ffmpeg-win-x86_64-v7.1.exe',
   'F:\\Python\\Lib\\site-packages\\imageio_ffmpeg\\binaries\\ffmpeg-win-x86_64-v7.1.exe',
   'BINARY'),
  ('ffmpeg_bundle\\bin\\ffmpeg.exe',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\bin\\ffmpeg.exe',
   'BINARY'),
  ('ffmpeg_bundle\\bin\\ffplay.exe',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\bin\\ffplay.exe',
   'BINARY'),
  ('ffmpeg_bundle\\bin\\ffprobe.exe',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\bin\\ffprobe.exe',
   'BINARY'),
  ('python313.dll', 'F:\\Python\\python313.dll', 'BINARY'),
  ('scipy.libs\\libscipy_openblas-6b2103f2ae4d8547998b5d188e9801fb.dll',
   'F:\\Python\\Lib\\site-packages\\scipy.libs\\libscipy_openblas-6b2103f2ae4d8547998b5d188e9801fb.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'F:\\Python\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'F:\\Python\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('cv2\\opencv_videoio_ffmpeg4120_64.dll',
   'F:\\Python\\Lib\\site-packages\\cv2\\opencv_videoio_ffmpeg4120_64.dll',
   'BINARY'),
  ('tkinterdnd2\\tkdnd\\win-x64\\libtkdnd2.9.4.dll',
   'F:\\Python\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x64\\libtkdnd2.9.4.dll',
   'BINARY'),
  ('_ctypes.pyd', 'F:\\Python\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('select.pyd', 'F:\\Python\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'F:\\Python\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'F:\\Python\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'F:\\Python\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'F:\\Python\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_overlapped.pyd', 'F:\\Python\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'F:\\Python\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_queue.pyd', 'F:\\Python\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'F:\\Python\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'F:\\Python\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'F:\\Python\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'F:\\Python\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'F:\\Python\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'F:\\Python\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'F:\\Python\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'F:\\Python\\Lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_nd_image.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\ndimage\\_nd_image.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_ni_label.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\ndimage\\_ni_label.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ellip_harm_2.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\special\\_ellip_harm_2.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_fblas.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_fblas.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_flapack.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_flapack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_lapack.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\cython_lapack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_blas.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\cython_blas.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_update.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_decomp_update.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_sqrtm_triu.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_sqrtm_triu.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_linalg_pythran.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_linalg_pythran.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_expm.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_expm.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_schur_sqrtm.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_schur_sqrtm.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_lu_cython.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_decomp_lu_cython.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\fft\\_pocketfft\\pypocketfft.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\pypocketfft.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_uarray\\_uarray.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\_uarray\\_uarray.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_solve_toeplitz.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_solve_toeplitz.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_cythonized_array_utils.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_cythonized_array_utils.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_zpropack.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_zpropack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_cpropack.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_cpropack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_dpropack.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_dpropack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_spropack.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_spropack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_dsolve\\_superlu.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\_superlu.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_csparsetools.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\_csparsetools.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_sparsetools.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\_sparsetools.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highspy\\_core.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_highspy\\_core.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highspy\\_highs_options.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_highspy\\_highs_options.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_direct.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_direct.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_qmc_cy.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_qmc_cy.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_sobol.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_sobol.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_wrap.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\spatial\\_distance_wrap.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_pybind.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\spatial\\_distance_pybind.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_hausdorff.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\spatial\\_hausdorff.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_tools.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_tools.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_reordering.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_reordering.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_matching.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_matching.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_flow.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_flow.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_min_spanning_tree.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_min_spanning_tree.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_traversal.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_traversal.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_shortest_path.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_shortest_path.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rgi_cython.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\_rgi_cython.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_interpnd.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\_interpnd.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rbfinterp_pythran.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\_rbfinterp_pythran.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_ppoly.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\_ppoly.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_dierckx.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\_dierckx.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_dfitpack.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\_dfitpack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_fitpack.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\interpolate\\_fitpack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\backends\\_backend_agg.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\backends\\_backend_agg.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_path.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\_path.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\markupsafe\\_speedups.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'F:\\Python\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('matplotlib\\_c_internal_utils.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\_c_internal_utils.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\backends\\_tkagg.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\backends\\_tkagg.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_image.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\_image.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\PIL\\_imaging.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('kiwisolver\\_cext.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\kiwisolver\\_cext.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_tri.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\_tri.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_qhull.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\_qhull.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('contourpy\\_contourpy.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\contourpy\\_contourpy.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\ft2font.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\ft2font.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats_pythran.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_stats_pythran.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_rcont\\rcont.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_rcont\\rcont.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_qmvnt_cy.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_qmvnt_cy.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_ansari_swilk_statistics.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_ansari_swilk_statistics.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_levy_stable\\levyst.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_levy_stable\\levyst.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_biasedurn.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_biasedurn.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\_cffi_backend.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_ccallback_c.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\_ccallback_c.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\stats\\_stats.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\cython_special.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\special\\cython_special.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\transform\\_rotation.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\spatial\\transform\\_rotation.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\transform\\_rigid_transform.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\spatial\\transform\\_rigid_transform.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_voronoi.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\spatial\\_voronoi.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_qhull.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\spatial\\_qhull.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_ckdtree.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\spatial\\_ckdtree.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_cyutility.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\_cyutility.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_pava_pybind.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_pava_pybind.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsq\\givens_elimination.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_lsq\\givens_elimination.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsap.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_lsap.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_interpolative.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\linalg\\_decomp_interpolative.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_bglu_dense.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_bglu_dense.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_slsqplib.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_slsqplib.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_zeros.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_zeros.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_minpack.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_minpack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_trlib\\_trlib.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_trlib\\_trlib.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_moduleTNC.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_moduleTNC.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lbfgsb.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_lbfgsb.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_group_columns.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\optimize\\_group_columns.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_lsoda.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_lsoda.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_dop.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_dop.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_vode.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_vode.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_quadpack.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_quadpack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_odepack.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\integrate\\_odepack.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_gufuncs.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\special\\_gufuncs.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_special_ufuncs.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\special\\_special_ufuncs.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_comb.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\special\\_comb.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_specfun.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\special\\_specfun.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\special\\_ufuncs.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs_cxx.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\special\\_ufuncs_cxx.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\numpy\\random\\_philox.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\numpy\\random\\_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\numpy\\random\\_common.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_rank_filter_1d.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\ndimage\\_rank_filter_1d.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_fpumode.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\_fpumode.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\messagestream.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\scipy\\_lib\\messagestream.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingft.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\PIL\\_imagingft.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd', 'F:\\Python\\DLLs\\_elementtree.pyd', 'EXTENSION'),
  ('PIL\\_imagingmath.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\PIL\\_imagingmath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\PIL\\_imagingtk.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\PIL\\_webp.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\PIL\\_imagingcms.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\hashing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\writers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\tslib.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\properties.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\ops.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\parsers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\util.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\cyextension\\util.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\resultproxy.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\cyextension\\resultproxy.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\processors.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\cyextension\\processors.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\immutabledict.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\cyextension\\immutabledict.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\collections.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\sqlalchemy\\cyextension\\collections.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('greenlet\\_greenlet.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\greenlet\\_greenlet.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd', 'F:\\Python\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('pandas\\_libs\\json.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\json.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\window\\indexers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\groupby.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\interval.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\index.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\hashtable.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\algos.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\arrays.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\join.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\sparse.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\missing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\internals.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\indexing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\lib.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\reshape.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_datetime.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\pandas_datetime.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_parser.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\pandas_parser.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\testing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sas.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\sas.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\byteswap.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\pandas\\_libs\\byteswap.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'F:\\Python\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('cv2\\cv2.pyd', 'F:\\Python\\Lib\\site-packages\\cv2\\cv2.pyd', 'EXTENSION'),
  ('_tkinter.pyd', 'F:\\Python\\DLLs\\_tkinter.pyd', 'EXTENSION'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'F:\\Python\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll', 'F:\\Python\\VCRUNTIME140_1.dll', 'BINARY'),
  ('libffi-8.dll', 'F:\\Python\\DLLs\\libffi-8.dll', 'BINARY'),
  ('libssl-3.dll', 'F:\\Python\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libcrypto-3.dll', 'F:\\Python\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('python3.dll', 'F:\\Python\\python3.dll', 'BINARY'),
  ('pywin32_system32\\pywintypes313.dll',
   'F:\\Python\\Lib\\site-packages\\pywin32_system32\\pywintypes313.dll',
   'BINARY'),
  ('sqlite3.dll', 'F:\\Python\\DLLs\\sqlite3.dll', 'BINARY'),
  ('pandas.libs\\msvcp140-1a0962f2a91a74c6d7136a768987a591.dll',
   'F:\\Python\\Lib\\site-packages\\pandas.libs\\msvcp140-1a0962f2a91a74c6d7136a768987a591.dll',
   'BINARY'),
  ('tcl86t.dll', 'F:\\Python\\DLLs\\tcl86t.dll', 'BINARY'),
  ('tk86t.dll', 'F:\\Python\\DLLs\\tk86t.dll', 'BINARY'),
  ('ucrtbase.dll',
   'F:\\Windows Kits\\10\\Windows Performance Toolkit\\ucrtbase.dll',
   'BINARY'),
  ('zlib1.dll', 'F:\\Python\\DLLs\\zlib1.dll', 'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'F:\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('ffmpeg_bundle\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\LICENSE',
   'DATA'),
  ('ffmpeg_bundle\\README.txt',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\README.txt',
   'DATA'),
  ('ffmpeg_bundle\\doc\\bootstrap.min.css',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\bootstrap.min.css',
   'DATA'),
  ('ffmpeg_bundle\\doc\\community.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\community.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\default.css',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\default.css',
   'DATA'),
  ('ffmpeg_bundle\\doc\\developer.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\developer.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\faq.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\faq.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\fate.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\fate.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\ffmpeg-all.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\ffmpeg-all.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\ffmpeg-bitstream-filters.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\ffmpeg-bitstream-filters.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\ffmpeg-codecs.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\ffmpeg-codecs.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\ffmpeg-devices.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\ffmpeg-devices.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\ffmpeg-filters.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\ffmpeg-filters.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\ffmpeg-formats.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\ffmpeg-formats.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\ffmpeg-protocols.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\ffmpeg-protocols.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\ffmpeg-resampler.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\ffmpeg-resampler.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\ffmpeg-scaler.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\ffmpeg-scaler.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\ffmpeg-utils.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\ffmpeg-utils.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\ffmpeg.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\ffmpeg.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\ffplay-all.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\ffplay-all.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\ffplay.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\ffplay.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\ffprobe-all.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\ffprobe-all.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\ffprobe.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\ffprobe.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\general.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\general.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\git-howto.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\git-howto.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\libavcodec.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\libavcodec.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\libavdevice.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\libavdevice.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\libavfilter.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\libavfilter.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\libavformat.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\libavformat.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\libavutil.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\libavutil.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\libswresample.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\libswresample.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\libswscale.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\libswscale.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\mailing-list-faq.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\mailing-list-faq.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\nut.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\nut.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\platform.html',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\platform.html',
   'DATA'),
  ('ffmpeg_bundle\\doc\\style.min.css',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\doc\\style.min.css',
   'DATA'),
  ('ffmpeg_bundle\\presets\\libvpx-1080p.ffpreset',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\presets\\libvpx-1080p.ffpreset',
   'DATA'),
  ('ffmpeg_bundle\\presets\\libvpx-1080p50_60.ffpreset',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\presets\\libvpx-1080p50_60.ffpreset',
   'DATA'),
  ('ffmpeg_bundle\\presets\\libvpx-360p.ffpreset',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\presets\\libvpx-360p.ffpreset',
   'DATA'),
  ('ffmpeg_bundle\\presets\\libvpx-720p.ffpreset',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\presets\\libvpx-720p.ffpreset',
   'DATA'),
  ('ffmpeg_bundle\\presets\\libvpx-720p50_60.ffpreset',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\ffmpeg_bundle\\presets\\libvpx-720p50_60.ffpreset',
   'DATA'),
  ('src\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\__init__.py',
   'DATA'),
  ('src\\__pycache__\\__init__.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('src\\core\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\core\\__init__.py',
   'DATA'),
  ('src\\core\\__pycache__\\__init__.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\core\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('src\\core\\__pycache__\\ab_video.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\core\\__pycache__\\ab_video.cpython-313.pyc',
   'DATA'),
  ('src\\core\\__pycache__\\image_to_video.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\core\\__pycache__\\image_to_video.cpython-313.pyc',
   'DATA'),
  ('src\\core\\__pycache__\\timestamp_control.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\core\\__pycache__\\timestamp_control.cpython-313.pyc',
   'DATA'),
  ('src\\core\\__pycache__\\video_deduplication.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\core\\__pycache__\\video_deduplication.cpython-313.pyc',
   'DATA'),
  ('src\\core\\ab_video.py',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\core\\ab_video.py',
   'DATA'),
  ('src\\core\\image_to_video.py',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\core\\image_to_video.py',
   'DATA'),
  ('src\\core\\timestamp_control.py',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\core\\timestamp_control.py',
   'DATA'),
  ('src\\core\\video_deduplication.py',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\core\\video_deduplication.py',
   'DATA'),
  ('src\\ffmpeg_setup.py',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\ffmpeg_setup.py',
   'DATA'),
  ('src\\gui\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\gui\\__init__.py',
   'DATA'),
  ('src\\gui\\__pycache__\\__init__.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\gui\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('src\\gui\\__pycache__\\drag_drop_frame.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\gui\\__pycache__\\drag_drop_frame.cpython-313.pyc',
   'DATA'),
  ('src\\gui\\__pycache__\\enhanced_window.cpython-313.pyc',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\gui\\__pycache__\\enhanced_window.cpython-313.pyc',
   'DATA'),
  ('src\\gui\\drag_drop_frame.py',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\gui\\drag_drop_frame.py',
   'DATA'),
  ('src\\gui\\enhanced_window.py',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\gui\\enhanced_window.py',
   'DATA'),
  ('src\\timestamp_processor.py',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\timestamp_processor.py',
   'DATA'),
  ('src\\video_processor.py',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\src\\video_processor.py',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'F:\\Python\\Lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'F:\\Python\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('imageio_ffmpeg\\binaries\\README.md',
   'F:\\Python\\Lib\\site-packages\\imageio_ffmpeg\\binaries\\README.md',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.png',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\goog.npz',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\goog.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.pdf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.svg',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark-palette.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark-palette.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.png',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-notebook.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-notebook.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-ticks.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-ticks.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\msft.csv',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\msft.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move_large.png',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.svg',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward_large.png',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help_large.png',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\README.txt',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\README.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.png',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.pdf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.svg',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.png',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.svg',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Stocks.csv',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Stocks.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.png',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\logo2.png',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\logo2.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\kpsewhich.lua',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\kpsewhich.lua',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-darkgrid.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-darkgrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back_large.png',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.png',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-muted.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-muted.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.pdf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.png',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.pdf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.png',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\petroff10.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\petroff10.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.svg',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.pdf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-colorblind.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-colorblind.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.svg',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.pdf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\matplotlibrc',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\matplotlibrc',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.pdf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-poster.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-poster.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-whitegrid.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-whitegrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.svg',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave_large.png',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-pastel.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-pastel.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home_large.png',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.pdf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.png',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-bright.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-bright.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-paper.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-paper.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-deep.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-deep.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots_large.png',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.svg',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-white.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-white.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-talk.mplstyle',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-talk.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.svg',
   'F:\\Python\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.svg',
   'DATA'),
  ('certifi\\py.typed',
   'F:\\Python\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'F:\\Python\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'F:\\Python\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'F:\\Python\\Lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('cv2\\config.py', 'F:\\Python\\Lib\\site-packages\\cv2\\config.py', 'DATA'),
  ('cv2\\config-3.py',
   'F:\\Python\\Lib\\site-packages\\cv2\\config-3.py',
   'DATA'),
  ('cv2\\load_config_py3.py',
   'F:\\Python\\Lib\\site-packages\\cv2\\load_config_py3.py',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-x64\\pkgIndex.tcl',
   'F:\\Python\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x64\\pkgIndex.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-x64\\tkdnd_compat.tcl',
   'F:\\Python\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x64\\tkdnd_compat.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-x64\\tkdnd_utils.tcl',
   'F:\\Python\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x64\\tkdnd_utils.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-x64\\tkdnd_unix.tcl',
   'F:\\Python\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x64\\tkdnd_unix.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-x64\\tkdnd_macosx.tcl',
   'F:\\Python\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x64\\tkdnd_macosx.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-x64\\tkdnd_generic.tcl',
   'F:\\Python\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x64\\tkdnd_generic.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-x64\\tkdnd.tcl',
   'F:\\Python\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x64\\tkdnd.tcl',
   'DATA'),
  ('tkinterdnd2\\tkdnd\\win-x64\\tkdnd_windows.tcl',
   'F:\\Python\\Lib\\site-packages\\tkinterdnd2\\tkdnd\\win-x64\\tkdnd_windows.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\kw.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'F:\\Python\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\ar.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'F:\\Python\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\GB', 'F:\\Python\\tcl\\tcl8.6\\tzdata\\GB', 'DATA'),
  ('_tk_data\\msgs\\el.msg', 'F:\\Python\\tcl\\tk8.6\\msgs\\el.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\WET', 'F:\\Python\\tcl\\tcl8.6\\tzdata\\WET', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tk_data\\text.tcl', 'F:\\Python\\tcl\\tk8.6\\text.tcl', 'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'F:\\Python\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tk_data\\icons.tcl', 'F:\\Python\\tcl\\tk8.6\\icons.tcl', 'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'F:\\Python\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'F:\\Python\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'F:\\Python\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'F:\\Python\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC', 'F:\\Python\\tcl\\tcl8.6\\tzdata\\ROC', 'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'F:\\Python\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\ro.msg', 'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tk_data\\msgs\\fi.msg', 'F:\\Python\\tcl\\tk8.6\\msgs\\fi.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\eo.msg', 'DATA'),
  ('_tcl_data\\auto.tcl', 'F:\\Python\\tcl\\tcl8.6\\auto.tcl', 'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg', 'F:\\Python\\tcl\\tk8.6\\msgs\\sv.msg', 'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\uk.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\af.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\EST', 'F:\\Python\\tcl\\tcl8.6\\tzdata\\EST', 'DATA'),
  ('_tk_data\\unsupported.tcl',
   'F:\\Python\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\sk.msg', 'DATA'),
  ('_tcl_data\\msgs\\hu.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\hu.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tk_data\\focus.tcl', 'F:\\Python\\tcl\\tk8.6\\focus.tcl', 'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'F:\\Python\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\sr.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tk_data\\entry.tcl', 'F:\\Python\\tcl\\tk8.6\\entry.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'F:\\Python\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tk_data\\msgs\\en.msg', 'F:\\Python\\tcl\\tk8.6\\msgs\\en.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\et.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\sh.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tk_data\\spinbox.tcl', 'F:\\Python\\tcl\\tk8.6\\spinbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tk_data\\license.terms', 'F:\\Python\\tcl\\tk8.6\\license.terms', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\ta.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\nb.msg', 'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'F:\\Python\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\ja.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ciudad_Juarez',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Ciudad_Juarez',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU', 'F:\\Python\\tcl\\tcl8.6\\tzdata\\W-SU', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba', 'F:\\Python\\tcl\\tcl8.6\\tzdata\\Cuba', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT', 'F:\\Python\\tcl\\tcl8.6\\tzdata\\UCT', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\history.tcl', 'F:\\Python\\tcl\\tcl8.6\\history.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'F:\\Python\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tk_data\\iconlist.tcl', 'F:\\Python\\tcl\\tk8.6\\iconlist.tcl', 'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'F:\\Python\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tk_data\\optMenu.tcl', 'F:\\Python\\tcl\\tk8.6\\optMenu.tcl', 'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'F:\\Python\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\kl.msg', 'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'F:\\Python\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\he.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tk_data\\palette.tcl', 'F:\\Python\\tcl\\tk8.6\\palette.tcl', 'DATA'),
  ('_tcl_data\\msgs\\ca.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\ca.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'F:\\Python\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\package.tcl', 'F:\\Python\\tcl\\tcl8.6\\package.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg', 'F:\\Python\\tcl\\tk8.6\\msgs\\nl.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl', 'F:\\Python\\tcl\\tk8.6\\mkpsenc.tcl', 'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'F:\\Python\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\it.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tk_data\\menu.tcl', 'F:\\Python\\tcl\\tk8.6\\menu.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\pt.msg', 'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\init.tcl', 'F:\\Python\\tcl\\tcl8.6\\init.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tk_data\\tkfbox.tcl', 'F:\\Python\\tcl\\tk8.6\\tkfbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\de.msg', 'DATA'),
  ('_tcl_data\\msgs\\fa.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\fa.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\pl.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\ms.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'F:\\Python\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\safe.tcl', 'F:\\Python\\tcl\\tcl8.6\\safe.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kyiv',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Kyiv',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'F:\\Python\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\te.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\sw.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'F:\\Python\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\gl.msg', 'DATA'),
  ('_tcl_data\\msgs\\id.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\id.msg', 'DATA'),
  ('_tcl_data\\msgs\\sq.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\sq.msg', 'DATA'),
  ('_tcl_data\\encoding\\koi8-t.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\koi8-t.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire', 'F:\\Python\\tcl\\tcl8.6\\tzdata\\Eire', 'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tk_data\\scale.tcl', 'F:\\Python\\tcl\\tk8.6\\scale.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\mk.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'F:\\Python\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl', 'F:\\Python\\tcl\\tk8.6\\pkgIndex.tcl', 'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'F:\\Python\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.8.tm',
   'F:\\Python\\tcl\\tcl8\\8.5\\tcltest-2.5.8.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\hi.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\vi.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\gv.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\tr.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg', 'F:\\Python\\tcl\\tk8.6\\msgs\\cs.msg', 'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'F:\\Python\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\es.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tk_data\\tclIndex', 'F:\\Python\\tcl\\tk8.6\\tclIndex', 'DATA'),
  ('_tk_data\\msgs\\fr.msg', 'F:\\Python\\tcl\\tk8.6\\msgs\\fr.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tk_data\\clrpick.tcl', 'F:\\Python\\tcl\\tk8.6\\clrpick.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\is.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\sv.msg', 'DATA'),
  ('_tk_data\\comdlg.tcl', 'F:\\Python\\tcl\\tk8.6\\comdlg.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\clock.tcl', 'F:\\Python\\tcl\\tcl8.6\\clock.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\tzdata\\HST', 'F:\\Python\\tcl\\tcl8.6\\tzdata\\HST', 'DATA'),
  ('_tcl_data\\tzdata\\NZ', 'F:\\Python\\tcl\\tcl8.6\\tzdata\\NZ', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tk_data\\msgbox.tcl', 'F:\\Python\\tcl\\tk8.6\\msgbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tk_data\\tearoff.tcl', 'F:\\Python\\tcl\\tk8.6\\tearoff.tcl', 'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'F:\\Python\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tk_data\\msgs\\de.msg', 'F:\\Python\\tcl\\tk8.6\\msgs\\de.msg', 'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'F:\\Python\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\mt.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\hr.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'F:\\Python\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\th.msg', 'DATA'),
  ('_tk_data\\msgs\\pt.msg', 'F:\\Python\\tcl\\tk8.6\\msgs\\pt.msg', 'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'F:\\Python\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'F:\\Python\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu', 'F:\\Python\\tcl\\tcl8.6\\tzdata\\Zulu', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\mr.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tk_data\\button.tcl', 'F:\\Python\\tcl\\tk8.6\\button.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tk_data\\scrlbar.tcl', 'F:\\Python\\tcl\\tk8.6\\scrlbar.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tk_data\\msgs\\zh_cn.msg',
   'F:\\Python\\tcl\\tk8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'F:\\Python\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tk_data\\msgs\\da.msg', 'F:\\Python\\tcl\\tk8.6\\msgs\\da.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\el.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg', 'F:\\Python\\tcl\\tk8.6\\msgs\\ru.msg', 'DATA'),
  ('_tcl_data\\msgs\\fo.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\fo.msg', 'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'F:\\Python\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\lt.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\parray.tcl', 'F:\\Python\\tcl\\tcl8.6\\parray.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\lv.msg', 'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT', 'F:\\Python\\tcl\\tcl8.6\\tzdata\\GMT', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tk_data\\listbox.tcl', 'F:\\Python\\tcl\\tk8.6\\listbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tk_data\\tk.tcl', 'F:\\Python\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'F:\\Python\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\tclIndex', 'F:\\Python\\tcl\\tcl8.6\\tclIndex', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK', 'F:\\Python\\tcl\\tcl8.6\\tzdata\\ROK', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\nn.msg', 'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'F:\\Python\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg', 'F:\\Python\\tcl\\tk8.6\\msgs\\pl.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tk_data\\images\\README',
   'F:\\Python\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tk_data\\bgerror.tcl', 'F:\\Python\\tcl\\tk8.6\\bgerror.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\nl.msg', 'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\ko.msg', 'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'F:\\Python\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'F:\\Python\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\sl.msg', 'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tk_data\\console.tcl', 'F:\\Python\\tcl\\tk8.6\\console.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\CET', 'F:\\Python\\tcl\\tcl8.6\\tzdata\\CET', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'F:\\Python\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'F:\\Python\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC', 'F:\\Python\\tcl\\tcl8.6\\tzdata\\PRC', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tk_data\\xmfbox.tcl', 'F:\\Python\\tcl\\tk8.6\\xmfbox.tcl', 'DATA'),
  ('_tk_data\\msgs\\es.msg', 'F:\\Python\\tcl\\tk8.6\\msgs\\es.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\tzdata\\MET', 'F:\\Python\\tcl\\tcl8.6\\tzdata\\MET', 'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'F:\\Python\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'F:\\Python\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\cs.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\fr.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran', 'F:\\Python\\tcl\\tcl8.6\\tzdata\\Iran', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tk_data\\dialog.tcl', 'F:\\Python\\tcl\\tk8.6\\dialog.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\word.tcl', 'F:\\Python\\tcl\\tcl8.6\\word.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\da.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\MST', 'F:\\Python\\tcl\\tcl8.6\\tzdata\\MST', 'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\EET', 'F:\\Python\\tcl\\tcl8.6\\tzdata\\EET', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg', 'F:\\Python\\tcl\\tk8.6\\msgs\\eo.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'F:\\Python\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\ru.msg', 'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tk_data\\msgs\\it.msg', 'F:\\Python\\tcl\\tk8.6\\msgs\\it.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-ru.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\koi8-ru.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.8.tm',
   'F:\\Python\\tcl\\tcl8\\8.6\\http-2.9.8.tm',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\be.msg', 'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'F:\\Python\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\bn.msg', 'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\fi.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC', 'F:\\Python\\tcl\\tcl8.6\\tzdata\\UTC', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg', 'F:\\Python\\tcl\\tk8.6\\msgs\\hu.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\bg.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'F:\\Python\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tk_data\\safetk.tcl', 'F:\\Python\\tcl\\tk8.6\\safetk.tcl', 'DATA'),
  ('_tk_data\\ttk\\ttk.tcl', 'F:\\Python\\tcl\\tk8.6\\ttk\\ttk.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tk_data\\choosedir.tcl', 'F:\\Python\\tcl\\tk8.6\\choosedir.tcl', 'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'F:\\Python\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tk_data\\obsolete.tcl', 'F:\\Python\\tcl\\tk8.6\\obsolete.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'F:\\Python\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\zh.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\eu.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'F:\\Python\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tm.tcl', 'F:\\Python\\tcl\\tcl8.6\\tm.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg', 'F:\\Python\\tcl\\tcl8.6\\msgs\\ga.msg', 'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'F:\\Python\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.19.tm',
   'F:\\Python\\tcl\\tcl8\\8.4\\platform-1.0.19.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0', 'F:\\Python\\tcl\\tcl8.6\\tzdata\\GMT0', 'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'F:\\Python\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'F:\\Python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'F:\\Python\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('numpy-2.2.6.dist-info\\LICENSE.txt',
   'F:\\Python\\Lib\\site-packages\\numpy-2.2.6.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\DELVEWHEEL',
   'F:\\Python\\Lib\\site-packages\\numpy-2.2.6.dist-info\\DELVEWHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'F:\\Python\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.6.dist-info\\INSTALLER',
   'F:\\Python\\Lib\\site-packages\\numpy-2.2.6.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'F:\\Python\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'F:\\Python\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'F:\\Python\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\RECORD',
   'F:\\Python\\Lib\\site-packages\\numpy-2.2.6.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'F:\\Python\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\entry_points.txt',
   'F:\\Python\\Lib\\site-packages\\numpy-2.2.6.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\WHEEL',
   'F:\\Python\\Lib\\site-packages\\numpy-2.2.6.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'F:\\Python\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.6.dist-info\\METADATA',
   'F:\\Python\\Lib\\site-packages\\numpy-2.2.6.dist-info\\METADATA',
   'DATA'),
  ('cv2\\__init__.py',
   'F:\\Python\\Lib\\site-packages\\cv2\\__init__.py',
   'DATA'),
  ('cv2\\version.py',
   'F:\\Python\\Lib\\site-packages\\cv2\\version.py',
   'DATA'),
  ('cv2\\utils\\__init__.py',
   'F:\\Python\\Lib\\site-packages\\cv2\\utils\\__init__.py',
   'DATA'),
  ('cv2\\typing\\__init__.py',
   'F:\\Python\\Lib\\site-packages\\cv2\\typing\\__init__.py',
   'DATA'),
  ('cv2\\misc\\version.py',
   'F:\\Python\\Lib\\site-packages\\cv2\\misc\\version.py',
   'DATA'),
  ('cv2\\misc\\__init__.py',
   'F:\\Python\\Lib\\site-packages\\cv2\\misc\\__init__.py',
   'DATA'),
  ('cv2\\mat_wrapper\\__init__.py',
   'F:\\Python\\Lib\\site-packages\\cv2\\mat_wrapper\\__init__.py',
   'DATA'),
  ('cv2\\gapi\\__init__.py',
   'F:\\Python\\Lib\\site-packages\\cv2\\gapi\\__init__.py',
   'DATA'),
  ('cv2\\data\\__init__.py',
   'F:\\Python\\Lib\\site-packages\\cv2\\data\\__init__.py',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\视频编辑\\build\\video_editor_final\\base_library.zip',
   'DATA')],)
