@echo off
chcp 65001 >nul
echo.
echo ========================================
echo   卡妙视频编辑软件 - 修复闪退问题
echo ========================================
echo.
echo 此工具专门修复EXE闪退问题
echo 不会修改任何主程序文件
echo.

echo 正在运行修复版打包...
python fix_build.py

if errorlevel 1 (
    echo.
    echo ❌ 修复打包失败
    echo 请检查错误信息
    pause
    exit /b 1
)

echo.
echo ========================================
echo         修复版打包完成！
echo ========================================
echo.
echo 📁 输出目录: dist\卡妙视频编辑软件_修复版\
echo 🚀 可执行文件: 卡妙视频编辑软件_修复版.exe
echo.
echo 💡 修复说明:
echo    - 已修复scipy模块依赖问题
echo    - 已修复unittest模块缺失问题
echo    - 已修复numpy.testing模块问题
echo.
echo 🎯 现在可以测试运行修复版程序了！
echo.

echo 是否现在打开输出目录？(Y/N)
set /p choice=请选择: 
if /i "%choice%"=="Y" (
    explorer "dist\卡妙视频编辑软件_修复版"
)

pause
